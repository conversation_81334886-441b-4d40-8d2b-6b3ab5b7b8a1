# Contributing to Prompt Enhancer

Thank you for your interest in contributing to Prompt Enhancer! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ and npm
- Git
- OpenAI API key for testing

### Development Setup

1. Fork the repository
2. Clone your fork:
   ```bash
   git clone https://github.com/yourusername/prompt-enhancer.git
   cd prompt-enhancer
   ```

3. Install dependencies:
   ```bash
   npm install
   ```

4. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your OpenAI API key
   ```

5. Start the development server:
   ```bash
   npm run electron:dev
   ```

## 📝 Development Guidelines

### Code Style

- Use TypeScript for all new code
- Follow the existing code style and formatting
- Use meaningful variable and function names
- Add comments for complex logic

### Commit Messages

Use conventional commit format:
- `feat:` for new features
- `fix:` for bug fixes
- `docs:` for documentation changes
- `style:` for formatting changes
- `refactor:` for code refactoring
- `test:` for adding tests
- `chore:` for maintenance tasks

Example: `feat: add support for custom keyboard shortcuts`

### Testing

- Test your changes on multiple platforms when possible
- Ensure the application builds successfully
- Test with different OpenAI models
- Verify keyboard shortcuts work correctly

## 🐛 Bug Reports

When reporting bugs, please include:

1. **Environment**: OS, Node.js version, app version
2. **Steps to reproduce**: Clear, step-by-step instructions
3. **Expected behavior**: What should happen
4. **Actual behavior**: What actually happens
5. **Screenshots**: If applicable
6. **Logs**: Check console output and application logs

## 💡 Feature Requests

For feature requests, please:

1. Check if the feature already exists or is planned
2. Describe the use case and benefits
3. Provide mockups or examples if helpful
4. Consider implementation complexity

## 🔧 Pull Requests

### Before Submitting

- Ensure your code follows the project's style guidelines
- Test your changes thoroughly
- Update documentation if needed
- Add or update tests for new functionality

### Pull Request Process

1. Create a feature branch from `main`
2. Make your changes
3. Test thoroughly
4. Update documentation
5. Submit a pull request with:
   - Clear title and description
   - Reference to related issues
   - Screenshots/videos if applicable

### Review Process

- All PRs require review before merging
- Address feedback promptly
- Keep PRs focused and reasonably sized
- Rebase on main if needed

## 📚 Documentation

Help improve documentation by:

- Fixing typos and grammar
- Adding examples and use cases
- Improving clarity and organization
- Translating to other languages

## 🤝 Community

- Be respectful and inclusive
- Help others in discussions
- Share your use cases and feedback
- Follow the code of conduct

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

## ❓ Questions

If you have questions about contributing, please:

1. Check existing documentation
2. Search closed issues
3. Open a new issue with the "question" label
4. Join our community discussions

Thank you for contributing to Prompt Enhancer! 🎉
