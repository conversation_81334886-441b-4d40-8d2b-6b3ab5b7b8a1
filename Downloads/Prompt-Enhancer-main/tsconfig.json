{"files": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}], "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "noImplicitAny": true, "noUnusedParameters": true, "skipLibCheck": true, "allowJs": true, "noUnusedLocals": true, "strictNullChecks": true, "strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "exactOptionalPropertyTypes": true}}