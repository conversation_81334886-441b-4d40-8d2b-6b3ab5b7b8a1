import { OpenAI } from 'openai';

export interface PromptAnalysis {
  complexity: 'simple' | 'moderate' | 'complex' | 'expert';
  domain: string;
  intent: 'question' | 'instruction' | 'creative' | 'analytical' | 'code' | 'research';
  tone: 'formal' | 'casual' | 'technical' | 'creative' | 'professional';
  clarity: number; // 0-100
  specificity: number; // 0-100
  completeness: number; // 0-100
  suggestions: string[];
  estimatedTokens: number;
  recommendedModel: string;
  industryTags: string[];
  confidenceScore: number;
}

export interface EnhancementMetrics {
  originalLength: number;
  enhancedLength: number;
  improvementScore: number;
  clarityImprovement: number;
  specificityImprovement: number;
  processingTime: number;
  modelUsed: string;
  tokensUsed: number;
  cost: number;
}

export class AIPromptAnalyzer {
  private openai: OpenAI;
  private industryTemplates: Map<string, string[]>;
  private domainKeywords: Map<string, string[]>;

  constructor(apiKey: string) {
    this.openai = new OpenAI({ apiKey });
    this.initializeTemplates();
    this.initializeDomainKeywords();
  }

  private initializeTemplates() {
    this.industryTemplates = new Map([
      ['software', [
        'Code review and optimization',
        'Architecture design patterns',
        'API documentation generation',
        'Bug analysis and debugging',
        'Performance optimization'
      ]],
      ['marketing', [
        'Brand voice development',
        'Campaign strategy creation',
        'Content marketing plans',
        'Customer persona analysis',
        'Market research insights'
      ]],
      ['research', [
        'Literature review synthesis',
        'Hypothesis generation',
        'Data analysis interpretation',
        'Methodology design',
        'Academic writing assistance'
      ]],
      ['business', [
        'Strategic planning',
        'Financial analysis',
        'Process optimization',
        'Risk assessment',
        'Stakeholder communication'
      ]],
      ['creative', [
        'Story development',
        'Character creation',
        'World building',
        'Creative writing prompts',
        'Artistic concept development'
      ]]
    ]);
  }

  private initializeDomainKeywords() {
    this.domainKeywords = new Map([
      ['software', ['code', 'function', 'algorithm', 'database', 'API', 'framework', 'debug', 'optimize']],
      ['marketing', ['brand', 'campaign', 'audience', 'conversion', 'engagement', 'ROI', 'strategy']],
      ['research', ['study', 'analysis', 'hypothesis', 'methodology', 'data', 'findings', 'literature']],
      ['business', ['strategy', 'revenue', 'profit', 'market', 'stakeholder', 'process', 'efficiency']],
      ['creative', ['story', 'character', 'plot', 'narrative', 'artistic', 'design', 'creative']]
    ]);
  }

  async analyzePrompt(prompt: string): Promise<PromptAnalysis> {
    const startTime = Date.now();
    
    // Basic analysis
    const wordCount = prompt.split(/\s+/).length;
    const estimatedTokens = Math.ceil(wordCount * 1.3); // Rough token estimation
    
    // Domain detection
    const domain = this.detectDomain(prompt);
    
    // Intent classification
    const intent = this.classifyIntent(prompt);
    
    // Tone analysis
    const tone = this.analyzeTone(prompt);
    
    // Complexity assessment
    const complexity = this.assessComplexity(prompt);
    
    // Quality metrics
    const clarity = this.assessClarity(prompt);
    const specificity = this.assessSpecificity(prompt);
    const completeness = this.assessCompleteness(prompt);
    
    // Industry tags
    const industryTags = this.extractIndustryTags(prompt);
    
    // Model recommendation
    const recommendedModel = this.recommendModel(complexity, estimatedTokens);
    
    // Generate suggestions
    const suggestions = await this.generateSuggestions(prompt, domain, intent);
    
    const processingTime = Date.now() - startTime;
    
    return {
      complexity,
      domain,
      intent,
      tone,
      clarity,
      specificity,
      completeness,
      suggestions,
      estimatedTokens,
      recommendedModel,
      industryTags,
      confidenceScore: this.calculateConfidenceScore(clarity, specificity, completeness)
    };
  }

  private detectDomain(prompt: string): string {
    const lowerPrompt = prompt.toLowerCase();
    let maxScore = 0;
    let detectedDomain = 'general';
    
    for (const [domain, keywords] of this.domainKeywords) {
      const score = keywords.reduce((acc, keyword) => {
        return acc + (lowerPrompt.includes(keyword) ? 1 : 0);
      }, 0);
      
      if (score > maxScore) {
        maxScore = score;
        detectedDomain = domain;
      }
    }
    
    return detectedDomain;
  }

  private classifyIntent(prompt: string): PromptAnalysis['intent'] {
    const lowerPrompt = prompt.toLowerCase();
    
    if (lowerPrompt.includes('?') || lowerPrompt.startsWith('what') || lowerPrompt.startsWith('how') || lowerPrompt.startsWith('why')) {
      return 'question';
    }
    if (lowerPrompt.includes('create') || lowerPrompt.includes('write') || lowerPrompt.includes('generate')) {
      return 'creative';
    }
    if (lowerPrompt.includes('analyze') || lowerPrompt.includes('compare') || lowerPrompt.includes('evaluate')) {
      return 'analytical';
    }
    if (lowerPrompt.includes('code') || lowerPrompt.includes('function') || lowerPrompt.includes('program')) {
      return 'code';
    }
    if (lowerPrompt.includes('research') || lowerPrompt.includes('study') || lowerPrompt.includes('investigate')) {
      return 'research';
    }
    
    return 'instruction';
  }

  private analyzeTone(prompt: string): PromptAnalysis['tone'] {
    const lowerPrompt = prompt.toLowerCase();
    
    if (lowerPrompt.includes('please') || lowerPrompt.includes('kindly') || lowerPrompt.includes('formal')) {
      return 'formal';
    }
    if (lowerPrompt.includes('technical') || lowerPrompt.includes('specification') || lowerPrompt.includes('documentation')) {
      return 'technical';
    }
    if (lowerPrompt.includes('creative') || lowerPrompt.includes('fun') || lowerPrompt.includes('imaginative')) {
      return 'creative';
    }
    if (lowerPrompt.includes('business') || lowerPrompt.includes('professional') || lowerPrompt.includes('corporate')) {
      return 'professional';
    }
    
    return 'casual';
  }

  private assessComplexity(prompt: string): PromptAnalysis['complexity'] {
    const wordCount = prompt.split(/\s+/).length;
    const sentenceCount = prompt.split(/[.!?]+/).length;
    const avgWordsPerSentence = wordCount / sentenceCount;
    
    if (wordCount < 10 && avgWordsPerSentence < 8) return 'simple';
    if (wordCount < 50 && avgWordsPerSentence < 15) return 'moderate';
    if (wordCount < 100 && avgWordsPerSentence < 25) return 'complex';
    return 'expert';
  }

  private assessClarity(prompt: string): number {
    let score = 100;
    
    // Deduct for vague words
    const vagueWords = ['thing', 'stuff', 'something', 'anything', 'maybe', 'perhaps'];
    vagueWords.forEach(word => {
      if (prompt.toLowerCase().includes(word)) score -= 10;
    });
    
    // Deduct for excessive length without structure
    const sentences = prompt.split(/[.!?]+/).filter(s => s.trim().length > 0);
    if (sentences.length > 5 && !prompt.includes('\n') && !prompt.includes('-')) {
      score -= 15;
    }
    
    return Math.max(0, score);
  }

  private assessSpecificity(prompt: string): number {
    let score = 50; // Base score
    
    // Add points for specific details
    if (prompt.includes('example') || prompt.includes('specifically')) score += 20;
    if (/\d+/.test(prompt)) score += 15; // Contains numbers
    if (prompt.includes('format') || prompt.includes('structure')) score += 15;
    if (prompt.length > 100) score += 10; // Longer prompts tend to be more specific
    
    return Math.min(100, score);
  }

  private assessCompleteness(prompt: string): number {
    let score = 60; // Base score
    
    // Check for context
    if (prompt.includes('context') || prompt.includes('background')) score += 15;
    
    // Check for desired output format
    if (prompt.includes('format') || prompt.includes('output') || prompt.includes('result')) score += 15;
    
    // Check for constraints or requirements
    if (prompt.includes('requirement') || prompt.includes('constraint') || prompt.includes('must')) score += 10;
    
    return Math.min(100, score);
  }

  private extractIndustryTags(prompt: string): string[] {
    const tags: string[] = [];
    const lowerPrompt = prompt.toLowerCase();
    
    for (const [industry, keywords] of this.domainKeywords) {
      const matches = keywords.filter(keyword => lowerPrompt.includes(keyword));
      if (matches.length > 0) {
        tags.push(industry);
      }
    }
    
    return tags;
  }

  private recommendModel(complexity: PromptAnalysis['complexity'], estimatedTokens: number): string {
    if (complexity === 'expert' || estimatedTokens > 1000) {
      return 'gpt-4o';
    }
    if (complexity === 'complex' || estimatedTokens > 500) {
      return 'gpt-4';
    }
    if (complexity === 'moderate') {
      return 'gpt-4o-mini';
    }
    return 'gpt-3.5-turbo';
  }

  private async generateSuggestions(prompt: string, domain: string, intent: PromptAnalysis['intent']): Promise<string[]> {
    const suggestions: string[] = [];
    
    // Basic suggestions based on analysis
    if (prompt.length < 20) {
      suggestions.push('Consider adding more context and specific details');
    }
    
    if (!prompt.includes('format') && intent === 'creative') {
      suggestions.push('Specify the desired output format (e.g., bullet points, paragraph, list)');
    }
    
    if (domain !== 'general') {
      const templates = this.industryTemplates.get(domain);
      if (templates && templates.length > 0) {
        suggestions.push(`Consider using ${domain} industry templates for better results`);
      }
    }
    
    if (intent === 'question' && !prompt.includes('example')) {
      suggestions.push('Add examples to clarify your question');
    }
    
    return suggestions;
  }

  private calculateConfidenceScore(clarity: number, specificity: number, completeness: number): number {
    return Math.round((clarity + specificity + completeness) / 3);
  }

  async calculateEnhancementMetrics(
    originalPrompt: string,
    enhancedPrompt: string,
    modelUsed: string,
    processingTime: number,
    tokensUsed: number
  ): Promise<EnhancementMetrics> {
    const originalAnalysis = await this.analyzePrompt(originalPrompt);
    const enhancedAnalysis = await this.analyzePrompt(enhancedPrompt);
    
    const improvementScore = enhancedAnalysis.confidenceScore - originalAnalysis.confidenceScore;
    const clarityImprovement = enhancedAnalysis.clarity - originalAnalysis.clarity;
    const specificityImprovement = enhancedAnalysis.specificity - originalAnalysis.specificity;
    
    // Rough cost calculation (varies by model)
    const costPerToken = modelUsed.includes('gpt-4') ? 0.00003 : 0.000002;
    const cost = tokensUsed * costPerToken;
    
    return {
      originalLength: originalPrompt.length,
      enhancedLength: enhancedPrompt.length,
      improvementScore,
      clarityImprovement,
      specificityImprovement,
      processingTime,
      modelUsed,
      tokensUsed,
      cost
    };
  }
}
