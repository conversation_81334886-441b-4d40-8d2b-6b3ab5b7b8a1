export interface PromptTemplate {
  id: string;
  name: string;
  category: string;
  description: string;
  template: string;
  variables: TemplateVariable[];
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  estimatedTokens: number;
  successRate: number;
  usageCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface TemplateVariable {
  name: string;
  type: 'text' | 'number' | 'select' | 'multiselect' | 'textarea';
  description: string;
  required: boolean;
  defaultValue?: any;
  options?: string[];
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
  };
}

export class TemplateEngine {
  private templates: Map<string, PromptTemplate>;
  private categories: string[];

  constructor() {
    this.templates = new Map();
    this.categories = [
      'Software Development',
      'Data Science',
      'Marketing',
      'Content Creation',
      'Business Analysis',
      'Research',
      'Education',
      'Creative Writing',
      'Technical Documentation',
      'Product Management'
    ];
    this.initializeDefaultTemplates();
  }

  private initializeDefaultTemplates() {
    const defaultTemplates: PromptTemplate[] = [
      {
        id: 'code-review',
        name: 'Code Review Assistant',
        category: 'Software Development',
        description: 'Comprehensive code review with security, performance, and best practices analysis',
        template: `Please conduct a thorough code review of the following {{language}} code:

**Code to Review:**
{{code}}

**Review Focus Areas:**
{{#if security}}✓ Security vulnerabilities and best practices{{/if}}
{{#if performance}}✓ Performance optimization opportunities{{/if}}
{{#if maintainability}}✓ Code maintainability and readability{{/if}}
{{#if testing}}✓ Testing coverage and quality{{/if}}

**Additional Context:**
- Project type: {{projectType}}
- Target environment: {{environment}}
- Performance requirements: {{performanceReqs}}

**Expected Output Format:**
1. **Overall Assessment** (1-10 score with justification)
2. **Critical Issues** (security, bugs, breaking changes)
3. **Improvement Suggestions** (performance, readability, best practices)
4. **Positive Aspects** (what's done well)
5. **Recommended Next Steps** (prioritized action items)

Please provide specific line-by-line feedback where applicable and suggest concrete improvements with code examples.`,
        variables: [
          {
            name: 'language',
            type: 'select',
            description: 'Programming language',
            required: true,
            options: ['JavaScript', 'TypeScript', 'Python', 'Java', 'C#', 'Go', 'Rust', 'C++', 'PHP', 'Ruby']
          },
          {
            name: 'code',
            type: 'textarea',
            description: 'Code to be reviewed',
            required: true,
            validation: { minLength: 10, maxLength: 5000 }
          },
          {
            name: 'projectType',
            type: 'select',
            description: 'Type of project',
            required: true,
            options: ['Web Application', 'Mobile App', 'API/Backend', 'Desktop Application', 'Library/Framework', 'Microservice']
          },
          {
            name: 'environment',
            type: 'select',
            description: 'Target environment',
            required: false,
            defaultValue: 'Production',
            options: ['Development', 'Staging', 'Production', 'Testing']
          },
          {
            name: 'performanceReqs',
            type: 'text',
            description: 'Performance requirements',
            required: false,
            defaultValue: 'Standard web application performance'
          }
        ],
        tags: ['code-review', 'software', 'quality-assurance', 'best-practices'],
        difficulty: 'intermediate',
        estimatedTokens: 800,
        successRate: 94,
        usageCount: 1247,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-15')
      },
      {
        id: 'marketing-campaign',
        name: 'Marketing Campaign Strategy',
        category: 'Marketing',
        description: 'Comprehensive marketing campaign planning and strategy development',
        template: `Create a comprehensive marketing campaign strategy for:

**Product/Service:** {{productName}}
**Target Audience:** {{targetAudience}}
**Campaign Objective:** {{objective}}
**Budget Range:** {{budget}}
**Timeline:** {{timeline}}

**Campaign Requirements:**
1. **Situation Analysis**
   - Market research insights
   - Competitor analysis
   - SWOT analysis

2. **Target Audience Profiling**
   - Demographics and psychographics
   - Customer journey mapping
   - Pain points and motivations

3. **Campaign Strategy**
   - Key messaging and value proposition
   - Channel selection and rationale
   - Content strategy and themes

4. **Tactical Execution Plan**
   - Specific tactics for each channel
   - Content calendar outline
   - Resource requirements

5. **Measurement & KPIs**
   - Success metrics and KPIs
   - Tracking and analytics setup
   - ROI projections

**Additional Context:**
- Industry: {{industry}}
- Geographic focus: {{geography}}
- Brand voice: {{brandVoice}}
- Key differentiators: {{differentiators}}

Please provide actionable recommendations with specific examples and best practices for each section.`,
        variables: [
          {
            name: 'productName',
            type: 'text',
            description: 'Product or service name',
            required: true,
            validation: { minLength: 2, maxLength: 100 }
          },
          {
            name: 'targetAudience',
            type: 'textarea',
            description: 'Target audience description',
            required: true,
            validation: { minLength: 10, maxLength: 500 }
          },
          {
            name: 'objective',
            type: 'select',
            description: 'Primary campaign objective',
            required: true,
            options: ['Brand Awareness', 'Lead Generation', 'Sales Conversion', 'Customer Retention', 'Product Launch', 'Market Expansion']
          },
          {
            name: 'budget',
            type: 'select',
            description: 'Budget range',
            required: true,
            options: ['Under $10K', '$10K-$50K', '$50K-$100K', '$100K-$500K', '$500K+']
          },
          {
            name: 'timeline',
            type: 'select',
            description: 'Campaign duration',
            required: true,
            options: ['1 month', '3 months', '6 months', '1 year', 'Ongoing']
          },
          {
            name: 'industry',
            type: 'text',
            description: 'Industry or sector',
            required: false,
            defaultValue: 'Technology'
          },
          {
            name: 'geography',
            type: 'text',
            description: 'Geographic focus',
            required: false,
            defaultValue: 'Global'
          },
          {
            name: 'brandVoice',
            type: 'select',
            description: 'Brand voice and tone',
            required: false,
            defaultValue: 'Professional',
            options: ['Professional', 'Casual', 'Authoritative', 'Friendly', 'Innovative', 'Trustworthy']
          },
          {
            name: 'differentiators',
            type: 'textarea',
            description: 'Key differentiators',
            required: false,
            validation: { maxLength: 300 }
          }
        ],
        tags: ['marketing', 'strategy', 'campaign', 'planning'],
        difficulty: 'advanced',
        estimatedTokens: 1200,
        successRate: 89,
        usageCount: 856,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-10')
      },
      {
        id: 'data-analysis',
        name: 'Data Analysis & Insights',
        category: 'Data Science',
        description: 'Comprehensive data analysis with statistical insights and recommendations',
        template: `Perform a comprehensive analysis of the following dataset:

**Dataset Description:** {{datasetDescription}}
**Analysis Objective:** {{analysisObjective}}
**Data Type:** {{dataType}}
**Sample Size:** {{sampleSize}}

**Analysis Requirements:**

1. **Exploratory Data Analysis (EDA)**
   - Data quality assessment
   - Descriptive statistics
   - Distribution analysis
   - Missing value patterns

2. **Statistical Analysis**
   - Correlation analysis
   - Trend identification
   - Outlier detection
   - {{#if hypothesis}}Hypothesis testing{{/if}}

3. **Key Insights & Patterns**
   - Significant findings
   - Unexpected patterns
   - Business implications
   - Actionable insights

4. **Visualizations Recommendations**
   - Suggested chart types
   - Key metrics to highlight
   - Dashboard design recommendations

5. **Next Steps & Recommendations**
   - Further analysis opportunities
   - Data collection improvements
   - Business action items

**Technical Context:**
- Tools available: {{tools}}
- Statistical significance level: {{significanceLevel}}
- Business context: {{businessContext}}

Please provide detailed methodology, statistical reasoning, and practical business recommendations.`,
        variables: [
          {
            name: 'datasetDescription',
            type: 'textarea',
            description: 'Description of the dataset',
            required: true,
            validation: { minLength: 20, maxLength: 1000 }
          },
          {
            name: 'analysisObjective',
            type: 'textarea',
            description: 'What you want to learn from the data',
            required: true,
            validation: { minLength: 10, maxLength: 500 }
          },
          {
            name: 'dataType',
            type: 'select',
            description: 'Primary data type',
            required: true,
            options: ['Numerical', 'Categorical', 'Time Series', 'Text', 'Mixed', 'Geospatial']
          },
          {
            name: 'sampleSize',
            type: 'select',
            description: 'Approximate sample size',
            required: true,
            options: ['< 1K', '1K-10K', '10K-100K', '100K-1M', '1M+']
          },
          {
            name: 'tools',
            type: 'multiselect',
            description: 'Available analysis tools',
            required: false,
            options: ['Python', 'R', 'SQL', 'Excel', 'Tableau', 'Power BI', 'SPSS', 'SAS']
          },
          {
            name: 'significanceLevel',
            type: 'select',
            description: 'Statistical significance level',
            required: false,
            defaultValue: '0.05',
            options: ['0.01', '0.05', '0.10']
          },
          {
            name: 'businessContext',
            type: 'textarea',
            description: 'Business context and constraints',
            required: false,
            validation: { maxLength: 300 }
          }
        ],
        tags: ['data-science', 'analytics', 'statistics', 'insights'],
        difficulty: 'advanced',
        estimatedTokens: 1000,
        successRate: 91,
        usageCount: 623,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-12')
      }
    ];

    defaultTemplates.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  getTemplate(id: string): PromptTemplate | undefined {
    return this.templates.get(id);
  }

  getTemplatesByCategory(category: string): PromptTemplate[] {
    return Array.from(this.templates.values())
      .filter(template => template.category === category)
      .sort((a, b) => b.successRate - a.successRate);
  }

  getAllTemplates(): PromptTemplate[] {
    return Array.from(this.templates.values())
      .sort((a, b) => b.usageCount - a.usageCount);
  }

  getCategories(): string[] {
    return this.categories;
  }

  searchTemplates(query: string): PromptTemplate[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.templates.values())
      .filter(template => 
        template.name.toLowerCase().includes(lowerQuery) ||
        template.description.toLowerCase().includes(lowerQuery) ||
        template.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
      )
      .sort((a, b) => b.successRate - a.successRate);
  }

  renderTemplate(templateId: string, variables: Record<string, any>): string {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new Error(`Template with id ${templateId} not found`);
    }

    let rendered = template.template;

    // Simple template rendering (replace {{variable}} with values)
    template.variables.forEach(variable => {
      const value = variables[variable.name] || variable.defaultValue || '';
      const regex = new RegExp(`{{${variable.name}}}`, 'g');
      rendered = rendered.replace(regex, value);
    });

    // Handle conditional blocks {{#if variable}}...{{/if}}
    rendered = rendered.replace(/{{#if\s+(\w+)}}(.*?){{\/if}}/gs, (match, varName, content) => {
      return variables[varName] ? content : '';
    });

    return rendered;
  }

  validateVariables(templateId: string, variables: Record<string, any>): { isValid: boolean; errors: string[] } {
    const template = this.templates.get(templateId);
    if (!template) {
      return { isValid: false, errors: ['Template not found'] };
    }

    const errors: string[] = [];

    template.variables.forEach(variable => {
      const value = variables[variable.name];

      if (variable.required && (!value || value.toString().trim() === '')) {
        errors.push(`${variable.name} is required`);
        return;
      }

      if (value && variable.validation) {
        const validation = variable.validation;
        const stringValue = value.toString();

        if (validation.minLength && stringValue.length < validation.minLength) {
          errors.push(`${variable.name} must be at least ${validation.minLength} characters`);
        }

        if (validation.maxLength && stringValue.length > validation.maxLength) {
          errors.push(`${variable.name} must be no more than ${validation.maxLength} characters`);
        }

        if (validation.pattern && !new RegExp(validation.pattern).test(stringValue)) {
          errors.push(`${variable.name} format is invalid`);
        }
      }
    });

    return { isValid: errors.length === 0, errors };
  }

  addTemplate(template: Omit<PromptTemplate, 'id' | 'createdAt' | 'updatedAt' | 'usageCount' | 'successRate'>): string {
    const id = `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newTemplate: PromptTemplate = {
      ...template,
      id,
      usageCount: 0,
      successRate: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.templates.set(id, newTemplate);
    return id;
  }

  updateTemplate(id: string, updates: Partial<PromptTemplate>): boolean {
    const template = this.templates.get(id);
    if (!template) {
      return false;
    }

    const updatedTemplate = {
      ...template,
      ...updates,
      id, // Ensure ID doesn't change
      updatedAt: new Date()
    };

    this.templates.set(id, updatedTemplate);
    return true;
  }

  deleteTemplate(id: string): boolean {
    return this.templates.delete(id);
  }

  incrementUsage(id: string): void {
    const template = this.templates.get(id);
    if (template) {
      template.usageCount++;
      template.updatedAt = new Date();
    }
  }

  updateSuccessRate(id: string, wasSuccessful: boolean): void {
    const template = this.templates.get(id);
    if (template) {
      // Simple success rate calculation (could be more sophisticated)
      const totalAttempts = template.usageCount;
      const currentSuccesses = Math.round((template.successRate / 100) * totalAttempts);
      const newSuccesses = wasSuccessful ? currentSuccesses + 1 : currentSuccesses;
      template.successRate = Math.round((newSuccesses / (totalAttempts + 1)) * 100);
      template.updatedAt = new Date();
    }
  }
}
