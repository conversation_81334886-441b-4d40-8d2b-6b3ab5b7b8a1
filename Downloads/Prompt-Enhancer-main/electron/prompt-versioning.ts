import { EventEmitter } from 'events';
import * as fs from 'fs';
import * as path from 'path';

export interface PromptVersion {
  id: string;
  promptId: string;
  version: number;
  content: string;
  metadata: {
    title: string;
    description?: string;
    tags: string[];
    category: string;
    author: string;
    createdAt: Date;
    updatedAt: Date;
  };
  analysis?: {
    complexity: string;
    clarity: number;
    specificity: number;
    completeness: number;
    estimatedTokens: number;
  };
  performance?: {
    successRate: number;
    avgImprovementScore: number;
    usageCount: number;
    userRating: number;
  };
  parentVersionId?: string;
  isPublished: boolean;
  collaborators: string[];
  comments: PromptComment[];
}

export interface PromptComment {
  id: string;
  author: string;
  content: string;
  createdAt: Date;
  lineNumber?: number;
  isResolved: boolean;
  replies: PromptComment[];
}

export interface PromptCollection {
  id: string;
  name: string;
  description: string;
  prompts: string[]; // Array of prompt IDs
  owner: string;
  collaborators: string[];
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
  tags: string[];
}

export class PromptVersioningSystem extends EventEmitter {
  private versions: Map<string, PromptVersion> = new Map();
  private collections: Map<string, PromptCollection> = new Map();
  private dataPath: string;

  constructor(dataPath: string = './data') {
    super();
    this.dataPath = dataPath;
    this.ensureDataDirectory();
    this.loadData();
  }

  private ensureDataDirectory() {
    if (!fs.existsSync(this.dataPath)) {
      fs.mkdirSync(this.dataPath, { recursive: true });
    }
  }

  private loadData() {
    try {
      const versionsPath = path.join(this.dataPath, 'versions.json');
      const collectionsPath = path.join(this.dataPath, 'collections.json');

      if (fs.existsSync(versionsPath)) {
        const versionsData = JSON.parse(fs.readFileSync(versionsPath, 'utf8'));
        this.versions = new Map(versionsData.map((v: any) => [v.id, {
          ...v,
          metadata: {
            ...v.metadata,
            createdAt: new Date(v.metadata.createdAt),
            updatedAt: new Date(v.metadata.updatedAt)
          },
          comments: v.comments.map((c: any) => ({
            ...c,
            createdAt: new Date(c.createdAt),
            replies: c.replies.map((r: any) => ({
              ...r,
              createdAt: new Date(r.createdAt)
            }))
          }))
        }]));
      }

      if (fs.existsSync(collectionsPath)) {
        const collectionsData = JSON.parse(fs.readFileSync(collectionsPath, 'utf8'));
        this.collections = new Map(collectionsData.map((c: any) => [c.id, {
          ...c,
          createdAt: new Date(c.createdAt),
          updatedAt: new Date(c.updatedAt)
        }]));
      }
    } catch (error) {
      console.error('Error loading versioning data:', error);
    }
  }

  private saveData() {
    try {
      const versionsPath = path.join(this.dataPath, 'versions.json');
      const collectionsPath = path.join(this.dataPath, 'collections.json');

      const versionsData = Array.from(this.versions.values());
      const collectionsData = Array.from(this.collections.values());

      fs.writeFileSync(versionsPath, JSON.stringify(versionsData, null, 2));
      fs.writeFileSync(collectionsPath, JSON.stringify(collectionsData, null, 2));
    } catch (error) {
      console.error('Error saving versioning data:', error);
    }
  }

  createPromptVersion(data: Omit<PromptVersion, 'id' | 'version' | 'comments'>): PromptVersion {
    const existingVersions = this.getVersionsByPromptId(data.promptId);
    const version = existingVersions.length + 1;
    
    const promptVersion: PromptVersion = {
      ...data,
      id: `${data.promptId}_v${version}_${Date.now()}`,
      version,
      comments: [],
      metadata: {
        ...data.metadata,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    };

    this.versions.set(promptVersion.id, promptVersion);
    this.saveData();
    this.emit('versionCreated', promptVersion);

    return promptVersion;
  }

  updatePromptVersion(versionId: string, updates: Partial<PromptVersion>): PromptVersion | null {
    const version = this.versions.get(versionId);
    if (!version) return null;

    const updatedVersion = {
      ...version,
      ...updates,
      metadata: {
        ...version.metadata,
        ...updates.metadata,
        updatedAt: new Date()
      }
    };

    this.versions.set(versionId, updatedVersion);
    this.saveData();
    this.emit('versionUpdated', updatedVersion);

    return updatedVersion;
  }

  getVersion(versionId: string): PromptVersion | null {
    return this.versions.get(versionId) || null;
  }

  getVersionsByPromptId(promptId: string): PromptVersion[] {
    return Array.from(this.versions.values())
      .filter(v => v.promptId === promptId)
      .sort((a, b) => b.version - a.version);
  }

  getLatestVersion(promptId: string): PromptVersion | null {
    const versions = this.getVersionsByPromptId(promptId);
    return versions[0] || null;
  }

  deleteVersion(versionId: string): boolean {
    const deleted = this.versions.delete(versionId);
    if (deleted) {
      this.saveData();
      this.emit('versionDeleted', versionId);
    }
    return deleted;
  }

  forkVersion(versionId: string, newAuthor: string, metadata: Partial<PromptVersion['metadata']>): PromptVersion | null {
    const originalVersion = this.versions.get(versionId);
    if (!originalVersion) return null;

    const forkedVersion = this.createPromptVersion({
      promptId: `${originalVersion.promptId}_fork_${Date.now()}`,
      content: originalVersion.content,
      metadata: {
        ...originalVersion.metadata,
        ...metadata,
        author: newAuthor,
        title: `${originalVersion.metadata.title} (Fork)`
      },
      analysis: originalVersion.analysis,
      parentVersionId: versionId,
      isPublished: false,
      collaborators: [newAuthor]
    });

    return forkedVersion;
  }

  addComment(versionId: string, comment: Omit<PromptComment, 'id' | 'createdAt' | 'replies'>): PromptComment | null {
    const version = this.versions.get(versionId);
    if (!version) return null;

    const newComment: PromptComment = {
      ...comment,
      id: `comment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
      replies: []
    };

    version.comments.push(newComment);
    this.versions.set(versionId, version);
    this.saveData();
    this.emit('commentAdded', { versionId, comment: newComment });

    return newComment;
  }

  replyToComment(versionId: string, commentId: string, reply: Omit<PromptComment, 'id' | 'createdAt' | 'replies'>): PromptComment | null {
    const version = this.versions.get(versionId);
    if (!version) return null;

    const comment = version.comments.find(c => c.id === commentId);
    if (!comment) return null;

    const newReply: PromptComment = {
      ...reply,
      id: `reply_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
      replies: []
    };

    comment.replies.push(newReply);
    this.versions.set(versionId, version);
    this.saveData();
    this.emit('replyAdded', { versionId, commentId, reply: newReply });

    return newReply;
  }

  resolveComment(versionId: string, commentId: string): boolean {
    const version = this.versions.get(versionId);
    if (!version) return false;

    const comment = version.comments.find(c => c.id === commentId);
    if (!comment) return false;

    comment.isResolved = true;
    this.versions.set(versionId, version);
    this.saveData();
    this.emit('commentResolved', { versionId, commentId });

    return true;
  }

  addCollaborator(versionId: string, collaboratorEmail: string): boolean {
    const version = this.versions.get(versionId);
    if (!version) return false;

    if (!version.collaborators.includes(collaboratorEmail)) {
      version.collaborators.push(collaboratorEmail);
      this.versions.set(versionId, version);
      this.saveData();
      this.emit('collaboratorAdded', { versionId, collaborator: collaboratorEmail });
      return true;
    }

    return false;
  }

  removeCollaborator(versionId: string, collaboratorEmail: string): boolean {
    const version = this.versions.get(versionId);
    if (!version) return false;

    const index = version.collaborators.indexOf(collaboratorEmail);
    if (index > -1) {
      version.collaborators.splice(index, 1);
      this.versions.set(versionId, version);
      this.saveData();
      this.emit('collaboratorRemoved', { versionId, collaborator: collaboratorEmail });
      return true;
    }

    return false;
  }

  createCollection(data: Omit<PromptCollection, 'id' | 'createdAt' | 'updatedAt'>): PromptCollection {
    const collection: PromptCollection = {
      ...data,
      id: `collection_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.collections.set(collection.id, collection);
    this.saveData();
    this.emit('collectionCreated', collection);

    return collection;
  }

  addToCollection(collectionId: string, promptId: string): boolean {
    const collection = this.collections.get(collectionId);
    if (!collection) return false;

    if (!collection.prompts.includes(promptId)) {
      collection.prompts.push(promptId);
      collection.updatedAt = new Date();
      this.collections.set(collectionId, collection);
      this.saveData();
      this.emit('promptAddedToCollection', { collectionId, promptId });
      return true;
    }

    return false;
  }

  removeFromCollection(collectionId: string, promptId: string): boolean {
    const collection = this.collections.get(collectionId);
    if (!collection) return false;

    const index = collection.prompts.indexOf(promptId);
    if (index > -1) {
      collection.prompts.splice(index, 1);
      collection.updatedAt = new Date();
      this.collections.set(collectionId, collection);
      this.saveData();
      this.emit('promptRemovedFromCollection', { collectionId, promptId });
      return true;
    }

    return false;
  }

  getCollection(collectionId: string): PromptCollection | null {
    return this.collections.get(collectionId) || null;
  }

  getCollectionsByOwner(owner: string): PromptCollection[] {
    return Array.from(this.collections.values())
      .filter(c => c.owner === owner || c.collaborators.includes(owner))
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  getPublicCollections(): PromptCollection[] {
    return Array.from(this.collections.values())
      .filter(c => c.isPublic)
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  searchVersions(query: string, filters?: {
    author?: string;
    category?: string;
    tags?: string[];
    isPublished?: boolean;
  }): PromptVersion[] {
    const lowerQuery = query.toLowerCase();
    
    return Array.from(this.versions.values())
      .filter(version => {
        // Text search
        const matchesQuery = !query || 
          version.metadata.title.toLowerCase().includes(lowerQuery) ||
          version.metadata.description?.toLowerCase().includes(lowerQuery) ||
          version.content.toLowerCase().includes(lowerQuery) ||
          version.metadata.tags.some(tag => tag.toLowerCase().includes(lowerQuery));

        // Filters
        const matchesAuthor = !filters?.author || version.metadata.author === filters.author;
        const matchesCategory = !filters?.category || version.metadata.category === filters.category;
        const matchesTags = !filters?.tags || filters.tags.every(tag => version.metadata.tags.includes(tag));
        const matchesPublished = filters?.isPublished === undefined || version.isPublished === filters.isPublished;

        return matchesQuery && matchesAuthor && matchesCategory && matchesTags && matchesPublished;
      })
      .sort((a, b) => b.metadata.updatedAt.getTime() - a.metadata.updatedAt.getTime());
  }

  exportVersion(versionId: string, format: 'json' | 'markdown' | 'txt'): string | null {
    const version = this.versions.get(versionId);
    if (!version) return null;

    switch (format) {
      case 'json':
        return JSON.stringify(version, null, 2);
      
      case 'markdown':
        return `# ${version.metadata.title}

**Version:** ${version.version}
**Author:** ${version.metadata.author}
**Category:** ${version.metadata.category}
**Created:** ${version.metadata.createdAt.toISOString()}
**Updated:** ${version.metadata.updatedAt.toISOString()}

${version.metadata.description ? `**Description:** ${version.metadata.description}\n` : ''}
**Tags:** ${version.metadata.tags.join(', ')}

## Content

\`\`\`
${version.content}
\`\`\`

${version.comments.length > 0 ? `## Comments

${version.comments.map(comment => `- **${comment.author}** (${comment.createdAt.toISOString()}): ${comment.content}`).join('\n')}` : ''}
`;

      case 'txt':
        return `${version.metadata.title}
${'='.repeat(version.metadata.title.length)}

Version: ${version.version}
Author: ${version.metadata.author}
Category: ${version.metadata.category}
Created: ${version.metadata.createdAt.toISOString()}
Updated: ${version.metadata.updatedAt.toISOString()}

${version.metadata.description ? `Description: ${version.metadata.description}\n` : ''}
Tags: ${version.metadata.tags.join(', ')}

Content:
--------
${version.content}

${version.comments.length > 0 ? `Comments:
---------
${version.comments.map(comment => `${comment.author} (${comment.createdAt.toISOString()}): ${comment.content}`).join('\n')}` : ''}
`;

      default:
        return null;
    }
  }

  getVersionStats(): {
    totalVersions: number;
    totalPrompts: number;
    totalCollections: number;
    avgVersionsPerPrompt: number;
    topCategories: Array<{ category: string; count: number }>;
    topAuthors: <AUTHORS>
  } {
    const versions = Array.from(this.versions.values());
    const uniquePrompts = new Set(versions.map(v => v.promptId));
    
    const categoryCount = new Map<string, number>();
    const authorCount = new Map<string, number>();

    versions.forEach(version => {
      categoryCount.set(version.metadata.category, (categoryCount.get(version.metadata.category) || 0) + 1);
      authorCount.set(version.metadata.author, (authorCount.get(version.metadata.author) || 0) + 1);
    });

    const topCategories = Array.from(categoryCount.entries())
      .map(([category, count]) => ({ category, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    const topAuthors = Array.from(authorCount.entries())
      .map(([author, count]) => ({ author, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    return {
      totalVersions: versions.length,
      totalPrompts: uniquePrompts.size,
      totalCollections: this.collections.size,
      avgVersionsPerPrompt: uniquePrompts.size > 0 ? versions.length / uniquePrompts.size : 0,
      topCategories,
      topAuthors
    };
  }
}

export const promptVersioning = new PromptVersioningSystem();
