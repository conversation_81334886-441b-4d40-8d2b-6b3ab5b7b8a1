import OpenAI from 'openai';
import { AIPromptAnalyzer, PromptAnalysis, EnhancementMetrics } from './ai-prompt-analyzer';

export type OpenAIModel = 'gpt-3.5-turbo' | 'gpt-4' | 'gpt-4o' | 'gpt-4o-mini' | 'gpt-4-turbo' | 'gpt-4-vision-preview';

export interface ModelCapabilities {
  id: OpenAIModel;
  name: string;
  description: string;
  maxTokens: number;
  costPer1kTokens: number;
  strengths: string[];
  bestFor: string[];
  speed: 'fast' | 'medium' | 'slow';
  quality: 'good' | 'excellent' | 'superior';
  supportsVision: boolean;
  supportsFunction: boolean;
}

export interface EnhancementRequest {
  prompt: string;
  systemPrompt: string;
  model: OpenAIModel;
  temperature?: number;
  maxTokens?: number;
  presencePenalty?: number;
  frequencyPenalty?: number;
  topP?: number;
  useAnalysis?: boolean;
}

export interface EnhancementResponse {
  enhancedText: string;
  analysis?: PromptAnalysis;
  metrics?: EnhancementMetrics;
  modelUsed: OpenAIModel;
  tokensUsed: number;
  processingTime: number;
  cost: number;
  confidence: number;
  suggestions: string[];
}

export class EnhancedOpenAIService {
  private openai: OpenAI;
  private analyzer: AIPromptAnalyzer | null = null;
  private modelCapabilities: Map<OpenAIModel, ModelCapabilities>;
  private usageStats: Map<string, number> = new Map();

  constructor() {
    this.openai = new OpenAI({ apiKey: 'placeholder' });
    this.initializeModelCapabilities();
  }

  private initializeModelCapabilities() {
    this.modelCapabilities = new Map([
      ['gpt-3.5-turbo', {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        description: 'Fast and cost-effective for most tasks',
        maxTokens: 4096,
        costPer1kTokens: 0.002,
        strengths: ['Speed', 'Cost-effectiveness', 'General tasks'],
        bestFor: ['Simple prompts', 'Quick iterations', 'High-volume processing'],
        speed: 'fast',
        quality: 'good',
        supportsVision: false,
        supportsFunction: true
      }],
      ['gpt-4', {
        id: 'gpt-4',
        name: 'GPT-4',
        description: 'More powerful with better reasoning capabilities',
        maxTokens: 8192,
        costPer1kTokens: 0.03,
        strengths: ['Reasoning', 'Complex tasks', 'Accuracy'],
        bestFor: ['Complex analysis', 'Professional content', 'Technical tasks'],
        speed: 'medium',
        quality: 'excellent',
        supportsVision: false,
        supportsFunction: true
      }],
      ['gpt-4-turbo', {
        id: 'gpt-4-turbo',
        name: 'GPT-4 Turbo',
        description: 'Optimized GPT-4 with improved performance and lower cost',
        maxTokens: 128000,
        costPer1kTokens: 0.01,
        strengths: ['Large context', 'Performance', 'Cost efficiency'],
        bestFor: ['Long documents', 'Complex projects', 'Detailed analysis'],
        speed: 'medium',
        quality: 'excellent',
        supportsVision: false,
        supportsFunction: true
      }],
      ['gpt-4o', {
        id: 'gpt-4o',
        name: 'GPT-4o',
        description: 'Latest model with improved performance across all tasks',
        maxTokens: 128000,
        costPer1kTokens: 0.005,
        strengths: ['Latest capabilities', 'Multimodal', 'Efficiency'],
        bestFor: ['Cutting-edge tasks', 'Multimodal content', 'Best results'],
        speed: 'medium',
        quality: 'superior',
        supportsVision: true,
        supportsFunction: true
      }],
      ['gpt-4o-mini', {
        id: 'gpt-4o-mini',
        name: 'GPT-4o Mini',
        description: 'Efficient version of GPT-4o with great performance',
        maxTokens: 128000,
        costPer1kTokens: 0.00015,
        strengths: ['Cost efficiency', 'Speed', 'Good quality'],
        bestFor: ['High-volume tasks', 'Quick processing', 'Budget-conscious projects'],
        speed: 'fast',
        quality: 'excellent',
        supportsVision: true,
        supportsFunction: true
      }],
      ['gpt-4-vision-preview', {
        id: 'gpt-4-vision-preview',
        name: 'GPT-4 Vision',
        description: 'GPT-4 with vision capabilities for image analysis',
        maxTokens: 4096,
        costPer1kTokens: 0.01,
        strengths: ['Vision analysis', 'Multimodal', 'Image understanding'],
        bestFor: ['Image analysis', 'Visual content', 'Multimodal tasks'],
        speed: 'slow',
        quality: 'excellent',
        supportsVision: true,
        supportsFunction: true
      }]
    ]);
  }

  setApiKey(apiKey: string) {
    this.openai = new OpenAI({ apiKey });
    this.analyzer = new AIPromptAnalyzer(apiKey);
  }

  async enhancePromptAdvanced(request: EnhancementRequest): Promise<EnhancementResponse> {
    const startTime = Date.now();
    
    // Analyze the prompt if requested
    let analysis: PromptAnalysis | undefined;
    if (request.useAnalysis && this.analyzer) {
      analysis = await this.analyzer.analyzePrompt(request.prompt);
      
      // Auto-select best model based on analysis if not specified
      if (!request.model && analysis.recommendedModel) {
        request.model = analysis.recommendedModel as OpenAIModel;
      }
    }

    const modelCapabilities = this.modelCapabilities.get(request.model);
    if (!modelCapabilities) {
      throw new Error(`Unsupported model: ${request.model}`);
    }

    // Optimize parameters based on model capabilities
    const optimizedRequest = this.optimizeRequestParameters(request, modelCapabilities);

    try {
      const response = await this.openai.chat.completions.create({
        model: optimizedRequest.model,
        messages: [
          {
            role: 'system',
            content: optimizedRequest.systemPrompt,
          },
          {
            role: 'user',
            content: optimizedRequest.prompt,
          },
        ],
        max_tokens: optimizedRequest.maxTokens,
        temperature: optimizedRequest.temperature,
        presence_penalty: optimizedRequest.presencePenalty,
        frequency_penalty: optimizedRequest.frequencyPenalty,
        top_p: optimizedRequest.topP,
      });

      const enhancedText = this.cleanEnhancedText(response.choices[0]?.message?.content?.trim() || '');
      const processingTime = Date.now() - startTime;
      const tokensUsed = response.usage?.total_tokens || 0;
      const cost = this.calculateCost(tokensUsed, request.model);

      // Calculate enhancement metrics if analyzer is available
      let metrics: EnhancementMetrics | undefined;
      if (this.analyzer) {
        metrics = await this.analyzer.calculateEnhancementMetrics(
          request.prompt,
          enhancedText,
          request.model,
          processingTime,
          tokensUsed
        );
      }

      // Generate suggestions
      const suggestions = this.generateSuggestions(analysis, enhancedText, request.model);

      // Update usage statistics
      this.updateUsageStats(request.model, tokensUsed, cost);

      return {
        enhancedText,
        analysis,
        metrics,
        modelUsed: request.model,
        tokensUsed,
        processingTime,
        cost,
        confidence: analysis?.confidenceScore || 85,
        suggestions
      };

    } catch (error) {
      console.error('Enhanced OpenAI API error:', error);
      throw new Error(`Failed to enhance prompt with ${request.model}. Please check your API key and try again.`);
    }
  }

  private optimizeRequestParameters(request: EnhancementRequest, capabilities: ModelCapabilities): EnhancementRequest {
    const optimized = { ...request };

    // Set default parameters based on model capabilities
    if (!optimized.temperature) {
      optimized.temperature = capabilities.quality === 'superior' ? 0.7 : 0.9;
    }

    if (!optimized.maxTokens) {
      optimized.maxTokens = Math.min(1000, Math.floor(capabilities.maxTokens * 0.3));
    }

    if (!optimized.presencePenalty) {
      optimized.presencePenalty = 0.1;
    }

    if (!optimized.frequencyPenalty) {
      optimized.frequencyPenalty = 0.1;
    }

    if (!optimized.topP) {
      optimized.topP = 0.9;
    }

    return optimized;
  }

  private cleanEnhancedText(text: string): string {
    let cleaned = text;

    // Remove common prefixes
    const prefixes = [
      /^\[ENHANCED\]\s*/i,
      /^\[ANSWER\]\s*/i,
      /^\[AGENT_TASK\]\s*/i,
      /^\[Enhanced Prompt\]\s*/i,
      /^\[Enhanced Agent Prompt\]\s*/i,
      /^\[Greeting Protocol\]\s*/i,
      /^improved prompt:\s*/i,
      /^enhanced prompt:\s*/i
    ];

    prefixes.forEach(prefix => {
      cleaned = cleaned.replace(prefix, '');
    });

    // Remove comments and explanations
    const patterns = [
      /^Note:.*$/im,
      /^Comment:.*$/im,
      /^Explanation:.*$/im,
      /\n\s*Note:.*$/im,
      /\n\s*Comment:.*$/im,
      /\n\s*Explanation:.*$/im
    ];

    patterns.forEach(pattern => {
      cleaned = cleaned.replace(pattern, '');
    });

    return cleaned.trim();
  }

  private calculateCost(tokens: number, model: OpenAIModel): number {
    const capabilities = this.modelCapabilities.get(model);
    if (!capabilities) return 0;
    
    return (tokens / 1000) * capabilities.costPer1kTokens;
  }

  private generateSuggestions(analysis: PromptAnalysis | undefined, enhancedText: string, model: OpenAIModel): string[] {
    const suggestions: string[] = [];

    if (analysis) {
      if (analysis.clarity < 80) {
        suggestions.push('Consider adding more specific details to improve clarity');
      }
      
      if (analysis.specificity < 70) {
        suggestions.push('Add examples or constraints to make the prompt more specific');
      }

      if (analysis.complexity === 'simple' && model.includes('gpt-4')) {
        suggestions.push('Consider using a more cost-effective model for simple prompts');
      }

      if (analysis.estimatedTokens > 2000 && model === 'gpt-3.5-turbo') {
        suggestions.push('Consider using GPT-4 Turbo for better handling of long prompts');
      }
    }

    if (enhancedText.length < 50) {
      suggestions.push('The enhanced prompt might benefit from more detailed instructions');
    }

    return suggestions;
  }

  private updateUsageStats(model: OpenAIModel, tokens: number, cost: number) {
    const key = `${model}_tokens`;
    this.usageStats.set(key, (this.usageStats.get(key) || 0) + tokens);
    
    const costKey = `${model}_cost`;
    this.usageStats.set(costKey, (this.usageStats.get(costKey) || 0) + cost);
  }

  getModelCapabilities(model?: OpenAIModel): ModelCapabilities | ModelCapabilities[] {
    if (model) {
      return this.modelCapabilities.get(model) || this.modelCapabilities.get('gpt-4o-mini')!;
    }
    return Array.from(this.modelCapabilities.values());
  }

  getUsageStats(): Record<string, number> {
    return Object.fromEntries(this.usageStats);
  }

  recommendModel(prompt: string, requirements?: {
    maxCost?: number;
    minQuality?: 'good' | 'excellent' | 'superior';
    maxProcessingTime?: 'fast' | 'medium' | 'slow';
  }): OpenAIModel {
    const models = Array.from(this.modelCapabilities.values());
    
    // Filter by requirements
    let candidates = models;
    
    if (requirements?.minQuality) {
      const qualityOrder = { 'good': 1, 'excellent': 2, 'superior': 3 };
      const minLevel = qualityOrder[requirements.minQuality];
      candidates = candidates.filter(m => qualityOrder[m.quality] >= minLevel);
    }

    if (requirements?.maxProcessingTime) {
      const speedOrder = { 'fast': 1, 'medium': 2, 'slow': 3 };
      const maxLevel = speedOrder[requirements.maxProcessingTime];
      candidates = candidates.filter(m => speedOrder[m.speed] <= maxLevel);
    }

    if (requirements?.maxCost) {
      candidates = candidates.filter(m => m.costPer1kTokens <= requirements.maxCost);
    }

    // Sort by quality and cost efficiency
    candidates.sort((a, b) => {
      const qualityOrder = { 'good': 1, 'excellent': 2, 'superior': 3 };
      const qualityDiff = qualityOrder[b.quality] - qualityOrder[a.quality];
      if (qualityDiff !== 0) return qualityDiff;
      
      return a.costPer1kTokens - b.costPer1kTokens; // Lower cost is better
    });

    return candidates[0]?.id || 'gpt-4o-mini';
  }
}

export const enhancedOpenAI = new EnhancedOpenAIService();
