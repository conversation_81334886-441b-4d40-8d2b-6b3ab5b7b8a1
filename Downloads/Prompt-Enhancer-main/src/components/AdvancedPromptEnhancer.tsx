import React, { useState, useEffect, use<PERSON><PERSON>back } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON><PERSON><PERSON>, 
  Brain, 
  Zap, 
  Target, 
  TrendingUp, 
  Clock, 
  DollarSign,
  Eye,
  Settings,
  Download,
  Share2,
  BookOpen,
  BarChart3,
  Lightbulb,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TooltipTrigger } from '@/components/ui/tooltip';

interface PromptAnalysis {
  complexity: 'simple' | 'moderate' | 'complex' | 'expert';
  domain: string;
  intent: 'question' | 'instruction' | 'creative' | 'analytical' | 'code' | 'research';
  tone: 'formal' | 'casual' | 'technical' | 'creative' | 'professional';
  clarity: number;
  specificity: number;
  completeness: number;
  suggestions: string[];
  estimatedTokens: number;
  recommendedModel: string;
  industryTags: string[];
  confidenceScore: number;
}

interface EnhancementMetrics {
  originalLength: number;
  enhancedLength: number;
  improvementScore: number;
  clarityImprovement: number;
  specificityImprovement: number;
  processingTime: number;
  modelUsed: string;
  tokensUsed: number;
  cost: number;
}

const AdvancedPromptEnhancer: React.FC = () => {
  const [originalPrompt, setOriginalPrompt] = useState('');
  const [enhancedPrompt, setEnhancedPrompt] = useState('');
  const [analysis, setAnalysis] = useState<PromptAnalysis | null>(null);
  const [metrics, setMetrics] = useState<EnhancementMetrics | null>(null);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [selectedModel, setSelectedModel] = useState('gpt-4o-mini');
  const [enhancementMode, setEnhancementMode] = useState('general');
  const [useAdvancedAnalysis, setUseAdvancedAnalysis] = useState(true);
  const [showMetrics, setShowMetrics] = useState(false);
  const [darkMode, setDarkMode] = useState(false);

  const models = [
    { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', cost: '$', speed: 'Fast' },
    { id: 'gpt-4', name: 'GPT-4', cost: '$$$', speed: 'Medium' },
    { id: 'gpt-4o', name: 'GPT-4o', cost: '$$', speed: 'Medium' },
    { id: 'gpt-4o-mini', name: 'GPT-4o Mini', cost: '$', speed: 'Fast' },
    { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', cost: '$$', speed: 'Medium' }
  ];

  const enhancementModes = [
    { id: 'general', name: 'General Enhancement', icon: Sparkles, description: 'All-purpose prompt improvement' },
    { id: 'agent', name: 'AI Agent', icon: Brain, description: 'Optimized for AI assistants' },
    { id: 'creative', name: 'Creative', icon: Lightbulb, description: 'Enhanced for creative tasks' },
    { id: 'analytical', name: 'Analytical', icon: BarChart3, description: 'Structured for analysis' },
    { id: 'code', name: 'Code', icon: Target, description: 'Programming and technical tasks' }
  ];

  const handleEnhance = useCallback(async () => {
    if (!originalPrompt.trim()) return;
    
    setIsEnhancing(true);
    setShowMetrics(false);
    
    try {
      // Simulate API call with realistic timing
      await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));
      
      // Mock analysis data
      const mockAnalysis: PromptAnalysis = {
        complexity: 'moderate',
        domain: 'software',
        intent: 'instruction',
        tone: 'professional',
        clarity: 75,
        specificity: 68,
        completeness: 82,
        suggestions: [
          'Add specific examples to improve clarity',
          'Consider defining the expected output format',
          'Include context about the target audience'
        ],
        estimatedTokens: Math.ceil(originalPrompt.length * 1.3),
        recommendedModel: 'gpt-4o-mini',
        industryTags: ['software', 'development'],
        confidenceScore: 85
      };

      const mockMetrics: EnhancementMetrics = {
        originalLength: originalPrompt.length,
        enhancedLength: originalPrompt.length * 1.8,
        improvementScore: 23,
        clarityImprovement: 18,
        specificityImprovement: 25,
        processingTime: 2500,
        modelUsed: selectedModel,
        tokensUsed: 450,
        cost: 0.0023
      };

      // Generate enhanced prompt
      const enhanced = `**Enhanced Prompt:**

${originalPrompt}

**Additional Context:**
- Please provide specific examples where applicable
- Structure your response with clear headings and bullet points
- Consider the target audience: technical professionals
- Expected output format: Detailed explanation with actionable steps

**Success Criteria:**
- Comprehensive coverage of the topic
- Clear, actionable recommendations
- Professional tone and structure
- Relevant examples and best practices`;

      setAnalysis(mockAnalysis);
      setMetrics(mockMetrics);
      setEnhancedPrompt(enhanced);
      setShowMetrics(true);
      
    } catch (error) {
      console.error('Enhancement failed:', error);
    } finally {
      setIsEnhancing(false);
    }
  }, [originalPrompt, selectedModel]);

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'simple': return 'bg-green-500';
      case 'moderate': return 'bg-yellow-500';
      case 'complex': return 'bg-orange-500';
      case 'expert': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <TooltipProvider>
      <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gradient-to-br from-blue-50 to-indigo-100'}`}>
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <motion.div 
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-8"
          >
            <div className="flex items-center justify-center gap-3 mb-4">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <Sparkles className="h-8 w-8 text-indigo-600" />
              </motion.div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                AI Prompt Enhancer Pro
              </h1>
            </div>
            <p className="text-gray-600 dark:text-gray-300 text-lg">
              Transform your prompts with intelligent analysis and professional enhancement
            </p>
          </motion.div>

          {/* Settings Bar */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="flex flex-wrap items-center justify-between gap-4 mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm"
          >
            <div className="flex items-center gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="dark-mode"
                  checked={darkMode}
                  onCheckedChange={setDarkMode}
                />
                <Label htmlFor="dark-mode">Dark Mode</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="advanced-analysis"
                  checked={useAdvancedAnalysis}
                  onCheckedChange={setUseAdvancedAnalysis}
                />
                <Label htmlFor="advanced-analysis">Advanced Analysis</Label>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <BookOpen className="h-4 w-4 mr-2" />
                Templates
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Input Section */}
            <motion.div 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="lg:col-span-2"
            >
              <Card className="h-full">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-5 w-5" />
                    Original Prompt
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Textarea
                    placeholder="Enter your prompt here... The AI will analyze and enhance it for better results."
                    value={originalPrompt}
                    onChange={(e) => setOriginalPrompt(e.target.value)}
                    className="min-h-[200px] resize-none"
                  />
                  
                  <div className="flex flex-wrap gap-4">
                    <div className="flex-1 min-w-[200px]">
                      <Label htmlFor="model-select" className="text-sm font-medium">
                        AI Model
                      </Label>
                      <Select value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger id="model-select">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {models.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              <div className="flex items-center justify-between w-full">
                                <span>{model.name}</span>
                                <div className="flex items-center gap-2 ml-2">
                                  <Badge variant="secondary" className="text-xs">
                                    {model.cost}
                                  </Badge>
                                  <Badge variant="outline" className="text-xs">
                                    {model.speed}
                                  </Badge>
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex-1 min-w-[200px]">
                      <Label htmlFor="mode-select" className="text-sm font-medium">
                        Enhancement Mode
                      </Label>
                      <Select value={enhancementMode} onValueChange={setEnhancementMode}>
                        <SelectTrigger id="mode-select">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {enhancementModes.map((mode) => (
                            <SelectItem key={mode.id} value={mode.id}>
                              <div className="flex items-center gap-2">
                                <mode.icon className="h-4 w-4" />
                                <span>{mode.name}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <Button 
                    onClick={handleEnhance}
                    disabled={!originalPrompt.trim() || isEnhancing}
                    className="w-full"
                    size="lg"
                  >
                    {isEnhancing ? (
                      <>
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="mr-2"
                        >
                          <Zap className="h-4 w-4" />
                        </motion.div>
                        Enhancing...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4 mr-2" />
                        Enhance Prompt
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            {/* Analysis Panel */}
            <motion.div 
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Card className="h-full">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Prompt Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {analysis ? (
                    <motion.div 
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="space-y-4"
                    >
                      {/* Complexity Badge */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Complexity</span>
                        <Badge className={`${getComplexityColor(analysis.complexity)} text-white`}>
                          {analysis.complexity}
                        </Badge>
                      </div>

                      {/* Quality Scores */}
                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Clarity</span>
                            <span className={getScoreColor(analysis.clarity)}>{analysis.clarity}%</span>
                          </div>
                          <Progress value={analysis.clarity} className="h-2" />
                        </div>
                        
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Specificity</span>
                            <span className={getScoreColor(analysis.specificity)}>{analysis.specificity}%</span>
                          </div>
                          <Progress value={analysis.specificity} className="h-2" />
                        </div>
                        
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Completeness</span>
                            <span className={getScoreColor(analysis.completeness)}>{analysis.completeness}%</span>
                          </div>
                          <Progress value={analysis.completeness} className="h-2" />
                        </div>
                      </div>

                      {/* Tags */}
                      <div>
                        <span className="text-sm font-medium mb-2 block">Industry Tags</span>
                        <div className="flex flex-wrap gap-1">
                          {analysis.industryTags.map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {/* Suggestions */}
                      <div>
                        <span className="text-sm font-medium mb-2 block">Suggestions</span>
                        <div className="space-y-2">
                          {analysis.suggestions.map((suggestion, index) => (
                            <div key={index} className="flex items-start gap-2 text-xs">
                              <Lightbulb className="h-3 w-3 text-yellow-500 mt-0.5 flex-shrink-0" />
                              <span className="text-gray-600 dark:text-gray-300">{suggestion}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  ) : (
                    <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                      <Brain className="h-12 w-12 mx-auto mb-3 opacity-50" />
                      <p>Enter a prompt to see analysis</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Enhanced Output */}
          <AnimatePresence>
            {enhancedPrompt && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: 0.4 }}
                className="mt-6"
              >
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500" />
                        Enhanced Prompt
                      </CardTitle>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Export
                        </Button>
                        <Button variant="outline" size="sm">
                          <Share2 className="h-4 w-4 mr-2" />
                          Share
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Tabs defaultValue="enhanced" className="w-full">
                      <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="enhanced">Enhanced</TabsTrigger>
                        <TabsTrigger value="comparison">Comparison</TabsTrigger>
                        <TabsTrigger value="metrics">Metrics</TabsTrigger>
                      </TabsList>
                      
                      <TabsContent value="enhanced" className="mt-4">
                        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                          <pre className="whitespace-pre-wrap text-sm">{enhancedPrompt}</pre>
                        </div>
                      </TabsContent>
                      
                      <TabsContent value="comparison" className="mt-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="font-medium mb-2">Original</h4>
                            <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-3 text-sm">
                              {originalPrompt}
                            </div>
                          </div>
                          <div>
                            <h4 className="font-medium mb-2">Enhanced</h4>
                            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3 text-sm">
                              {enhancedPrompt}
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                      
                      <TabsContent value="metrics" className="mt-4">
                        {metrics && (
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                              <TrendingUp className="h-6 w-6 mx-auto mb-2 text-blue-600" />
                              <div className="text-2xl font-bold text-blue-600">+{metrics.improvementScore}%</div>
                              <div className="text-xs text-gray-600 dark:text-gray-300">Improvement</div>
                            </div>
                            <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                              <Clock className="h-6 w-6 mx-auto mb-2 text-green-600" />
                              <div className="text-2xl font-bold text-green-600">{metrics.processingTime}ms</div>
                              <div className="text-xs text-gray-600 dark:text-gray-300">Processing Time</div>
                            </div>
                            <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                              <Zap className="h-6 w-6 mx-auto mb-2 text-purple-600" />
                              <div className="text-2xl font-bold text-purple-600">{metrics.tokensUsed}</div>
                              <div className="text-xs text-gray-600 dark:text-gray-300">Tokens Used</div>
                            </div>
                            <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                              <DollarSign className="h-6 w-6 mx-auto mb-2 text-yellow-600" />
                              <div className="text-2xl font-bold text-yellow-600">${metrics.cost.toFixed(4)}</div>
                              <div className="text-xs text-gray-600 dark:text-gray-300">Cost</div>
                            </div>
                          </div>
                        )}
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default AdvancedPromptEnhancer;
