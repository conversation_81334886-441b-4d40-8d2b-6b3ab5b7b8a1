import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BarChart3,
  TrendingUp,
  Users,
  Clock,
  DollarSign,
  Zap,
  Target,
  Award,
  Calendar,
  Download,
  Filter,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface AnalyticsData {
  totalPrompts: number;
  successRate: number;
  avgImprovementScore: number;
  totalCost: number;
  totalTokens: number;
  avgProcessingTime: number;
  topModels: Array<{ name: string; usage: number; successRate: number }>;
  dailyUsage: Array<{ date: string; prompts: number; cost: number }>;
  categoryBreakdown: Array<{ category: string; count: number; percentage: number }>;
  userActivity: Array<{ user: string; prompts: number; lastActive: string }>;
  performanceMetrics: {
    clarityImprovement: number;
    specificityImprovement: number;
    completenessImprovement: number;
    userSatisfaction: number;
  };
}

const AnalyticsDashboard: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [timeRange, setTimeRange] = useState('7d');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading analytics data
    const loadAnalytics = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const mockData: AnalyticsData = {
        totalPrompts: 1247,
        successRate: 94.2,
        avgImprovementScore: 23.8,
        totalCost: 45.67,
        totalTokens: 892456,
        avgProcessingTime: 2340,
        topModels: [
          { name: 'GPT-4o Mini', usage: 45, successRate: 96.2 },
          { name: 'GPT-4', usage: 32, successRate: 94.8 },
          { name: 'GPT-4o', usage: 18, successRate: 97.1 },
          { name: 'GPT-3.5 Turbo', usage: 5, successRate: 89.3 }
        ],
        dailyUsage: [
          { date: '2024-01-15', prompts: 156, cost: 6.78 },
          { date: '2024-01-16', prompts: 189, cost: 8.23 },
          { date: '2024-01-17', prompts: 203, cost: 9.45 },
          { date: '2024-01-18', prompts: 178, cost: 7.89 },
          { date: '2024-01-19', prompts: 234, cost: 10.12 },
          { date: '2024-01-20', prompts: 167, cost: 7.34 },
          { date: '2024-01-21', prompts: 120, cost: 5.86 }
        ],
        categoryBreakdown: [
          { category: 'Software Development', count: 423, percentage: 34 },
          { category: 'Content Creation', count: 312, percentage: 25 },
          { category: 'Data Analysis', count: 187, percentage: 15 },
          { category: 'Marketing', count: 156, percentage: 12.5 },
          { category: 'Research', count: 124, percentage: 10 },
          { category: 'Other', count: 45, percentage: 3.5 }
        ],
        userActivity: [
          { user: '<EMAIL>', prompts: 89, lastActive: '2 hours ago' },
          { user: '<EMAIL>', prompts: 67, lastActive: '4 hours ago' },
          { user: '<EMAIL>', prompts: 54, lastActive: '1 day ago' },
          { user: '<EMAIL>', prompts: 43, lastActive: '2 days ago' }
        ],
        performanceMetrics: {
          clarityImprovement: 28.5,
          specificityImprovement: 31.2,
          completenessImprovement: 25.8,
          userSatisfaction: 4.7
        }
      };
      
      setAnalyticsData(mockData);
      setIsLoading(false);
    };

    loadAnalytics();
  }, [timeRange]);

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    change?: number;
    icon: React.ElementType;
    color: string;
    suffix?: string;
  }> = ({ title, value, change, icon: Icon, color, suffix = '' }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">{title}</p>
              <p className="text-2xl font-bold">
                {value}{suffix}
              </p>
              {change !== undefined && (
                <p className={`text-xs ${change >= 0 ? 'text-green-600' : 'text-red-600'} flex items-center mt-1`}>
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {change >= 0 ? '+' : ''}{change}% from last period
                </p>
              )}
            </div>
            <div className={`p-3 rounded-full ${color}`}>
              <Icon className="h-6 w-6 text-white" />
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <RefreshCw className="h-8 w-8 text-indigo-600" />
        </motion.div>
      </div>
    );
  }

  if (!analyticsData) return null;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h2>
          <p className="text-muted-foreground">
            Comprehensive insights into your prompt enhancement usage
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Total Prompts"
          value={analyticsData.totalPrompts.toLocaleString()}
          change={12.5}
          icon={BarChart3}
          color="bg-blue-500"
        />
        <StatCard
          title="Success Rate"
          value={analyticsData.successRate}
          change={2.3}
          icon={Target}
          color="bg-green-500"
          suffix="%"
        />
        <StatCard
          title="Avg Improvement"
          value={analyticsData.avgImprovementScore}
          change={5.7}
          icon={TrendingUp}
          color="bg-purple-500"
          suffix="%"
        />
        <StatCard
          title="Total Cost"
          value={`$${analyticsData.totalCost}`}
          change={-8.2}
          icon={DollarSign}
          color="bg-orange-500"
        />
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Model Usage */}
            <Card>
              <CardHeader>
                <CardTitle>Model Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analyticsData.topModels.map((model, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium">{model.name}</span>
                          <span className="text-sm text-muted-foreground">{model.usage}%</span>
                        </div>
                        <Progress value={model.usage} className="h-2" />
                        <div className="flex items-center justify-between mt-1">
                          <span className="text-xs text-muted-foreground">Success Rate</span>
                          <Badge variant="secondary" className="text-xs">
                            {model.successRate}%
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Category Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Usage by Category</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analyticsData.categoryBreakdown.map((category, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{category.category}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">{category.count}</span>
                        <Badge variant="outline" className="text-xs">
                          {category.percentage}%
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>Quality Improvements</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Clarity Improvement</span>
                      <span className="font-medium">+{analyticsData.performanceMetrics.clarityImprovement}%</span>
                    </div>
                    <Progress value={analyticsData.performanceMetrics.clarityImprovement} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Specificity Improvement</span>
                      <span className="font-medium">+{analyticsData.performanceMetrics.specificityImprovement}%</span>
                    </div>
                    <Progress value={analyticsData.performanceMetrics.specificityImprovement} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Completeness Improvement</span>
                      <span className="font-medium">+{analyticsData.performanceMetrics.completenessImprovement}%</span>
                    </div>
                    <Progress value={analyticsData.performanceMetrics.completenessImprovement} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Processing Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Processing Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-blue-500" />
                      <span className="text-sm font-medium">Avg Processing Time</span>
                    </div>
                    <span className="text-sm font-bold">{analyticsData.avgProcessingTime}ms</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm font-medium">Total Tokens</span>
                    </div>
                    <span className="text-sm font-bold">{analyticsData.totalTokens.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <div className="flex items-center gap-2">
                      <Award className="h-4 w-4 text-green-500" />
                      <span className="text-sm font-medium">User Satisfaction</span>
                    </div>
                    <span className="text-sm font-bold">{analyticsData.performanceMetrics.userSatisfaction}/5.0</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="usage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Daily Usage Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.dailyUsage.map((day, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{new Date(day.date).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <div className="text-sm font-medium">{day.prompts} prompts</div>
                        <div className="text-xs text-muted-foreground">${day.cost.toFixed(2)}</div>
                      </div>
                      <Progress value={(day.prompts / 250) * 100} className="w-20 h-2" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.userActivity.map((user, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                        {user.user.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <div className="font-medium">{user.user}</div>
                        <div className="text-sm text-muted-foreground">Last active: {user.lastActive}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{user.prompts} prompts</div>
                      <Badge variant="secondary" className="text-xs">
                        Active
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AnalyticsDashboard;
