.tab-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  width: 100%;
  border-radius: 0.5rem;
  background-color: #f1f5f9;
  padding: 0.25rem;
  gap: 0.25rem;
}

.tab-button {
  position: relative;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #64748b;
}

.tab-button:hover:not(.active) {
  background-color: rgba(255, 255, 255, 0.5);
  color: #334155;
}

.tab-button.active {
  background-color: white;
  color: #0f172a;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Animated border effect */
.tab-button.active::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 0.5rem;
  background: linear-gradient(90deg, #4f46e5, #7c3aed, #ec4899, #4f46e5);
  background-size: 300% 300%;
  animation: moveGradient 3s ease infinite;
  z-index: -1;
}

@keyframes moveGradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
